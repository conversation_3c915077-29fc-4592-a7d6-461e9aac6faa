"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Loader2, Music, Sparkles, Lock, Unlock } from "lucide-react";
import { toast } from "sonner";

const musicFormSchema = z.object({
  prompt: z.string().min(10, "Prompt must be at least 10 characters").max(500, "Prompt must be less than 500 characters"),
  style: z.string().optional(),
  mood: z.string().optional(),
  duration: z.enum(["15", "30", "60"]),
  bpm: z.number().min(60).max(200).optional(),
  bpmLocked: z.boolean().default(false),
  // 移除 provider 字段，让后端自动选择
});

type MusicFormValues = z.infer<typeof musicFormSchema>;

interface MusicFormProps {
  onSubmit: (values: MusicFormValues) => Promise<void>;
  isLoading?: boolean;
  userCredits?: number;
}

const MUSIC_STYLES = [
  "ambient", "electronic", "chill", "upbeat", "corporate",
  "cinematic", "jazz", "classical", "rock", "pop", "hip-hop",
  "techno", "house", "trance", "dubstep", "drum-and-bass",
  "lofi", "synthwave", "minimal", "experimental"
];

const MUSIC_MOODS = [
  "happy", "sad", "energetic", "calm", "mysterious",
  "dramatic", "romantic", "aggressive", "peaceful", "dark",
  "bright", "melancholic", "euphoric", "tense", "relaxed"
];

const DURATION_COSTS = {
  "15": 1,
  "30": 2,
  "60": 3,
};

export default function MusicForm({ onSubmit, isLoading = false, userCredits = 0 }: MusicFormProps) {
  const [selectedStyle, setSelectedStyle] = useState<string>("");
  const [selectedMood, setSelectedMood] = useState<string>("");

  const form = useForm<MusicFormValues>({
    resolver: zodResolver(musicFormSchema),
    defaultValues: {
      prompt: "",
      style: "",
      mood: "",
      duration: "30",
      bpm: undefined,
      bpmLocked: false,
      // 不设置 provider，让后端自动选择
    },
  });

  const watchedDuration = form.watch("duration");
  const requiredCredits = DURATION_COSTS[watchedDuration as keyof typeof DURATION_COSTS];
  const canGenerate = userCredits >= requiredCredits;

  const handleSubmit = async (values: MusicFormValues) => {
    if (!canGenerate) {
      toast.error(`Insufficient credits. Required: ${requiredCredits}, Available: ${userCredits}`);
      return;
    }

    try {
      await onSubmit(values);
      toast.success("Music generation started!");
      form.reset();
      setSelectedStyle("");
      setSelectedMood("");
    } catch (error) {
      toast.error("Failed to start music generation");
      console.error("Music generation error:", error);
    }
  };

  const addStyle = (style: string) => {
    if (selectedStyle !== style) {
      setSelectedStyle(style);
      form.setValue("style", style);
    }
  };

  const addMood = (mood: string) => {
    if (selectedMood !== mood) {
      setSelectedMood(mood);
      form.setValue("mood", mood);
    }
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Music className="h-5 w-5" />
          Generate Music Loop
        </CardTitle>
        <CardDescription>
          Create AI-generated seamless music loops for your projects
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Prompt */}
            <FormField
              control={form.control}
              name="prompt"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Music Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe the music you want to generate... e.g., 'Upbeat electronic music with a driving beat, perfect for workout videos'"
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Describe the style, mood, instruments, or feeling you want in your music
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Style Selection */}
            <div className="space-y-3">
              <FormLabel>Style (Optional)</FormLabel>
              <div className="flex flex-wrap gap-2">
                {MUSIC_STYLES.map((style) => (
                  <Badge
                    key={style}
                    variant={selectedStyle === style ? "default" : "outline"}
                    className="cursor-pointer hover:bg-primary/80"
                    onClick={() => addStyle(style)}
                  >
                    {style}
                  </Badge>
                ))}
              </div>
              <FormField
                control={form.control}
                name="style"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        placeholder="Or type a custom style..."
                        {...field}
                        onChange={(e) => {
                          field.onChange(e);
                          setSelectedStyle(e.target.value);
                        }}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            {/* Mood Selection */}
            <div className="space-y-3">
              <FormLabel>Mood (Optional)</FormLabel>
              <div className="flex flex-wrap gap-2">
                {MUSIC_MOODS.map((mood) => (
                  <Badge
                    key={mood}
                    variant={selectedMood === mood ? "default" : "outline"}
                    className="cursor-pointer hover:bg-primary/80"
                    onClick={() => addMood(mood)}
                  >
                    {mood}
                  </Badge>
                ))}
              </div>
              <FormField
                control={form.control}
                name="mood"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        placeholder="Or type a custom mood..."
                        {...field}
                        onChange={(e) => {
                          field.onChange(e);
                          setSelectedMood(e.target.value);
                        }}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Duration */}
              <FormField
                control={form.control}
                name="duration"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Duration</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select duration" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="15">15 seconds (1 credit)</SelectItem>
                        <SelectItem value="30">30 seconds (2 credits)</SelectItem>
                        <SelectItem value="60">60 seconds (3 credits)</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* AI Provider 部分完全移除，不显示任何相关信息 */}
            </div>

            {/* BPM Control */}
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="bpmLocked"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                    <div className="space-y-0.5">
                      <FormLabel className="flex items-center gap-2">
                        {field.value ? <Lock className="h-4 w-4" /> : <Unlock className="h-4 w-4" />}
                        BPM Lock
                      </FormLabel>
                      <FormDescription>
                        {field.value ? "BPM is locked to the specified value" : "BPM will be automatically detected"}
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="bpm"
                render={({ field }) => {
                  const bpmLocked = form.watch("bpmLocked");
                  return (
                    <FormItem>
                      <FormLabel>BPM {bpmLocked ? "(Locked)" : "(Optional)"}</FormLabel>
                      <FormControl>
                        <div className="space-y-3">
                          <Slider
                            min={60}
                            max={200}
                            step={5}
                            value={[field.value || 120]}
                            onValueChange={(value) => field.onChange(value[0])}
                            className="w-full"
                            disabled={!bpmLocked}
                          />
                          <div className="flex justify-between text-sm text-muted-foreground">
                            <span>60 BPM</span>
                            <span className={`font-medium ${bpmLocked ? "text-primary" : ""}`}>
                              {field.value || 120} BPM
                            </span>
                            <span>200 BPM</span>
                          </div>
                        </div>
                      </FormControl>
                      <FormDescription>
                        {bpmLocked
                          ? "The generated music will match this exact BPM"
                          : "Enable BPM lock to specify exact tempo"
                        }
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  );
                }}
              />
            </div>

            {/* Credits Info */}
            <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
              <div className="text-sm">
                <span className="text-muted-foreground">Cost: </span>
                <span className="font-medium">{requiredCredits} credits</span>
              </div>
              <div className="text-sm">
                <span className="text-muted-foreground">Available: </span>
                <span className={`font-medium ${canGenerate ? "text-green-600" : "text-red-600"}`}>
                  {userCredits} credits
                </span>
              </div>
            </div>

            {/* Submit Button */}
            <Button
              type="submit"
              className="w-full"
              disabled={isLoading || !canGenerate}
              size="lg"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Generating Music...
                </>
              ) : (
                <>
                  <Sparkles className="mr-2 h-4 w-4" />
                  Generate Music Loop
                </>
              )}
            </Button>

            {!canGenerate && (
              <p className="text-sm text-red-600 text-center">
                Insufficient credits. Please purchase more credits to continue.
              </p>
            )}
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
