"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { TechCard, TechCardContent } from "@/components/ui/tech-card";
import { Button } from "@/components/ui/button";
import { TechButton } from "@/components/ui/tech-button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import {
  Music,
  Sparkles,
  Clock,
  Zap,
  CheckCircle,
  AlertCircle,
  Loader2,
  Play,
  Download,
  History
} from "lucide-react";
import { cn } from "@/lib/utils";
import { toast } from "sonner";
import { Track } from "@/types/music";
import { generateTrackUrl } from "@/lib/track-slug";
import Link from "next/link";
import { useRouter } from "next/navigation";
import SimpleMusicForm from "./simple-music-form";
import AudioPlayer from "../player/audio-player";
import LoopTestPlayer from "../player/loop-test-player";
import { useAppContext } from "@/contexts/app";
import InsufficientCreditsModal from "./insufficient-credits-modal";

interface MusicGeneratorPageProps {
  locale: string;
}

interface GenerationResult {
  generation_uuid: string;
  uuid?: string; // For database records
  prompt?: string;
  style?: string;
  mood?: string;
  bpm?: number;
  duration?: number;
  status: "pending" | "processing" | "completed" | "failed";
  estimated_completion_time?: number;
  track?: Track;
  error?: string;
  created_at?: string | Date;
}

export default function MusicGeneratorPage({ locale }: MusicGeneratorPageProps) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { setShowSignModal } = useAppContext();
  const [userCredits, setUserCredits] = useState(0);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [currentGeneration, setCurrentGeneration] = useState<GenerationResult | null>(null);
  const [recentGenerations, setRecentGenerations] = useState<GenerationResult[]>([]);
  const [showInsufficientCreditsModal, setShowInsufficientCreditsModal] = useState(false);
  const [insufficientCreditsData, setInsufficientCreditsData] = useState<{
    currentCredits: number;
    requiredCredits: number;
    duration: number;
  } | null>(null);

  useEffect(() => {
    // 只有在用户已登录时才加载数据
    if (status === "authenticated" && session?.user) {
      loadUserCredits();
      loadRecentGenerations();
    }
  }, [status, session]);

  // 监听登录状态变化，用户登录成功后自动加载数据
  useEffect(() => {
    if (status === "authenticated" && session?.user) {
      // 用户刚登录成功，加载必要数据
      loadUserCredits();
      loadRecentGenerations();
    }
  }, [status]);

  // 定期刷新积分余额（每30秒）
  useEffect(() => {
    if (status === "authenticated" && session?.user) {
      const interval = setInterval(() => {
        loadUserCredits();
      }, 30000); // 30 seconds

      return () => clearInterval(interval);
    }
  }, [status, session]);

  const loadUserCredits = async () => {
    // 检查用户是否已登录
    if (status !== "authenticated" || !session?.user) {
      console.log("User not authenticated, skipping credits load");
      setUserCredits(0);
      return;
    }

    try {
      const response = await fetch("/api/user/credits");
      const data = await response.json();
      if (response.ok && data.success) {
        setUserCredits(data.credits || 0);
      } else if (response.status === 401) {
        console.log("Authentication required for user credits");
        setUserCredits(0);
      } else {
        console.error("Failed to load user credits:", data.error || "Unknown error");
      }
    } catch (error) {
      console.error("Failed to load user credits:", error);
    }
  };

  const loadRecentGenerations = async () => {
    // 检查用户是否已登录
    if (status !== "authenticated" || !session?.user) {
      console.log("User not authenticated, skipping recent generations load");
      return;
    }

    try {
      const response = await fetch("/api/music/generations?limit=5");
      const data = await response.json();

      if (response.ok && data.success) {
        // Transform database records to GenerationResult format
        const transformedGenerations = (data.generations || []).map((gen: any) => ({
          generation_uuid: gen.uuid,
          uuid: gen.uuid,
          prompt: gen.prompt,
          style: gen.style,
          mood: gen.mood,
          bpm: gen.bpm,
          duration: gen.duration,
          status: gen.status,
          created_at: gen.created_at,
          // Track will be loaded separately if needed
        }));
        setRecentGenerations(transformedGenerations);
      } else if (response.status === 401) {
        console.log("Authentication required for recent generations");
        // 不显示错误，因为这是正常的未登录状态
      } else {
        console.error("Failed to load recent generations:", data.error || "Unknown error");
      }
    } catch (error) {
      console.error("Failed to load recent generations:", error);
    }
  };

  const handleMusicGeneration = async (values: any) => {
    setIsGenerating(true);
    setGenerationProgress(0);

    try {
      const response = await fetch("/api/music/generate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          prompt: values.prompt,
          style: values.style,
          mood: values.mood,
          duration: parseInt(values.duration),
          bpm: values.bpmLocked ? values.bmp : undefined,
          provider: values.provider,
        }),
      });

      const data = await response.json();

      if (data.code === 0) {
        const generation: GenerationResult = {
          generation_uuid: data.data.generation_uuid,
          status: data.data.status,
          estimated_completion_time: data.data.estimated_completion_time,
        };

        setCurrentGeneration(generation);
        toast.success("Music generation started!");

        // Update user credits
        await loadUserCredits();

        // Poll for completion
        pollGenerationStatus(generation.generation_uuid, generation.estimated_completion_time || 60);
      } else {
        // 检查是否是积分不足错误
        if (data.message && data.message.includes("Insufficient credits")) {
          const duration = parseInt(values.duration);
          const requiredCredits = duration === 15 ? 1 : duration === 30 ? 2 : 3;

          setInsufficientCreditsData({
            currentCredits: userCredits,
            requiredCredits,
            duration,
          });
          setShowInsufficientCreditsModal(true);
          setIsGenerating(false);
          return;
        }
        throw new Error(data.message);
      }
    } catch (error) {
      console.error("Music generation failed:", error);
      toast.error("Failed to start music generation");
      setIsGenerating(false);
    }
  };

  const pollGenerationStatus = async (generationUuid: string, estimatedTime: number) => {
    const startTime = Date.now();
    const pollInterval = 3000; // 3 seconds

    const poll = async () => {
      try {
        const response = await fetch(`/api/music/status?generation_uuid=${generationUuid}`);
        const data = await response.json();

        if (data.code === 0) {
          const status = data.data.status;

          if (status === "completed") {
            setCurrentGeneration(prev => prev ? {
              ...prev,
              status: "completed",
              track: data.data.track,
            } : null);
            setGenerationProgress(100);
            setIsGenerating(false);
            toast.success("Music generation completed!");
            
            // Reload recent generations
            await loadRecentGenerations();
            return;
          } else if (status === "failed") {
            setCurrentGeneration(prev => prev ? {
              ...prev,
              status: "failed",
              error: data.data.error || "Generation failed",
            } : null);
            setIsGenerating(false);
            toast.error("Music generation failed");
            return;
          }
        }

        // Update progress based on elapsed time
        const elapsed = Date.now() - startTime;
        const progress = Math.min((elapsed / (estimatedTime * 1000)) * 100, 95);
        setGenerationProgress(progress);

        // Continue polling
        setTimeout(poll, pollInterval);
      } catch (error) {
        console.error("Failed to poll generation status:", error);
        setTimeout(poll, pollInterval);
      }
    };

    poll();
  };

  const resetGeneration = () => {
    setCurrentGeneration(null);
    setIsGenerating(false);
    setGenerationProgress(0);
  };

  // 如果正在加载认证状态，显示加载指示器
  if (status === "loading") {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Loading...</span>
          </div>
        </div>
      </div>
    );
  }

  // 检查用户是否已认证
  const isAuthenticated = status === "authenticated";

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto py-12 px-4">
        <div className="max-w-4xl mx-auto">
          {/* Simplified Header */}
          <div className="text-center mb-12">
            <h1 className="text-5xl font-bold tracking-tight mb-4">
              <span className="bg-gradient-to-r from-foreground via-primary to-foreground bg-clip-text text-transparent">
                Create Music
              </span>
            </h1>
            <p className="text-lg text-muted-foreground mb-6">
              Generate AI music loops in seconds
            </p>

            {/* Compact Credits Display - only show for authenticated users */}
            {isAuthenticated && (
              <div className="inline-flex items-center gap-2 bg-muted/50 px-4 py-2 rounded-full">
                <Zap className="h-4 w-4 text-primary" />
                <span className="font-medium">{userCredits} credits</span>
              </div>
            )}
          </div>

          {/* Main Content with overlay for unauthenticated users */}
          <div className="relative">
            {/* Overlay for unauthenticated users */}
            {!isAuthenticated && (
              <div className="absolute inset-0 z-10 bg-background/80 backdrop-blur-sm flex items-center justify-center">
                <Card className="max-w-md mx-auto">
                  <CardContent className="pt-6">
                    <div className="text-center space-y-4">
                      <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto" />
                      <h3 className="text-lg font-semibold">Sign In Required</h3>
                      <p className="text-muted-foreground">
                        Please sign in to generate AI music loops and access your generation history.
                      </p>
                      <Button
                        onClick={() => setShowSignModal(true)}
                        className="w-full"
                      >
                        Sign In to Continue
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            <div className={`space-y-8 ${!isAuthenticated ? 'pointer-events-none select-none' : ''}`}>
            {/* Generation Form */}
            <SimpleMusicForm
              onSubmit={handleMusicGeneration}
              isLoading={isGenerating}
              userCredits={userCredits}
            />

            {/* Generation Progress */}
            {isGenerating && currentGeneration && (
              <TechCard variant="neon" glow={true} className="max-w-2xl mx-auto">
                <TechCardContent className="p-6">
                  <div className="text-center space-y-4">
                    <div className="flex items-center justify-center gap-3">
                      <Loader2 className="h-6 w-6 animate-spin text-primary" />
                      <h3 className="text-lg font-semibold">Creating your music...</h3>
                    </div>

                    <div className="space-y-2">
                      <Progress value={generationProgress} className="w-full h-2" />
                      <p className="text-sm text-muted-foreground">
                        {Math.round(generationProgress)}% complete • Usually takes ~{currentGeneration.estimated_completion_time || 60}s
                      </p>
                    </div>
                  </div>
                </TechCardContent>
              </TechCard>
            )}

            {/* Generation Result */}
            {currentGeneration?.status === "completed" && currentGeneration.track && (
              <TechCard variant="glass" className="max-w-2xl mx-auto">
                <TechCardContent className="p-6">
                  <div className="text-center space-y-6">
                    <div className="flex items-center justify-center gap-2">
                      <CheckCircle className="h-6 w-6 text-green-500" />
                      <h3 className="text-xl font-semibold">Music Ready!</h3>
                    </div>

                    <div>
                      <h4 className="font-medium text-lg mb-2">{currentGeneration.track.title}</h4>
                      <div className="flex items-center justify-center gap-3 mb-3">
                        {currentGeneration.track.style && (
                          <Badge variant="secondary">{currentGeneration.track.style}</Badge>
                        )}
                        {currentGeneration.track.mood && (
                          <Badge variant="outline">{currentGeneration.track.mood}</Badge>
                        )}
                        {currentGeneration.track.bpm && (
                          <Badge variant="outline">{currentGeneration.track.bpm} BPM</Badge>
                        )}
                      </div>
                    </div>

                    <div className="space-y-4">
                      <LoopTestPlayer
                        track={currentGeneration.track}
                        autoStart={false}
                        maxLoops={3}
                      />
                    </div>

                    <div className="flex gap-3 justify-center">
                      <TechButton
                        onClick={() => {
                          const link = document.createElement("a");
                          link.href = currentGeneration.track!.file_url;
                          link.download = `${currentGeneration.track!.title}.mp3`;
                          document.body.appendChild(link);
                          link.click();
                          document.body.removeChild(link);
                        }}
                        variant="neon"
                      >
                        <Download className="h-4 w-4 mr-2" />
                        Download
                      </TechButton>
                      <TechButton onClick={resetGeneration} variant="glass">
                        Generate Another
                      </TechButton>
                    </div>
                  </div>
                </TechCardContent>
              </TechCard>
            )}

            {/* Generation Error */}
            {currentGeneration?.status === "failed" && (
              <TechCard variant="glass" className="max-w-2xl mx-auto border-red-200">
                <TechCardContent className="p-6">
                  <div className="text-center space-y-4">
                    <div className="flex items-center justify-center gap-2">
                      <AlertCircle className="h-6 w-6 text-red-500" />
                      <h3 className="text-lg font-semibold text-red-600">Generation Failed</h3>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {currentGeneration.error || "Something went wrong. Please try again."}
                    </p>
                    <TechButton onClick={resetGeneration} variant="outline">
                      Try Again
                    </TechButton>
                  </div>
                </TechCardContent>
              </TechCard>
            )}

            {/* Recent Generations - Simplified */}
            {!isGenerating && !currentGeneration && recentGenerations.length > 0 && (
              <div className="max-w-2xl mx-auto">
                <h3 className="text-lg font-semibold mb-4 text-center">Recent Generations</h3>
                <div className="grid grid-cols-1 gap-3">
                  {recentGenerations.slice(0, 3).map((generation) => {
                    const trackUrl = generation.track ? generateTrackUrl({
                      prompt: generation.prompt || generation.track.prompt,
                      title: generation.track.title,
                      bpm: generation.track.bpm || generation.bpm,
                      uuid: generation.track.uuid,
                      style: generation.track.style || generation.style,
                      slug: generation.track.slug
                    }) : null;

                    return (
                      <div
                        key={generation.generation_uuid}
                        className={cn(
                          "p-4 rounded-lg border transition-all duration-200",
                          generation.status === "completed" && trackUrl
                            ? "hover:border-primary cursor-pointer hover:bg-muted/50"
                            : "opacity-60"
                        )}
                        onClick={() => {
                          if (generation.status === "completed" && trackUrl) {
                            router.push(trackUrl);
                          }
                        }}
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="font-medium text-sm">
                              {generation.track?.title || generation.prompt || "Untitled"}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {generation.track?.style && `${generation.track.style} • `}
                              {generation.track?.duration && `${generation.track.duration}s`}
                            </div>
                          </div>
                          <Badge
                            variant={generation.status === "completed" ? "default" : "secondary"}
                            className="text-xs"
                          >
                            {generation.status}
                          </Badge>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Insufficient Credits Modal */}
      {insufficientCreditsData && (
        <InsufficientCreditsModal
          isOpen={showInsufficientCreditsModal}
          onClose={() => {
            setShowInsufficientCreditsModal(false);
            setInsufficientCreditsData(null);
          }}
          currentCredits={insufficientCreditsData.currentCredits}
          requiredCredits={insufficientCreditsData.requiredCredits}
          duration={insufficientCreditsData.duration}
        />
      )}
    </div>
  );
}
