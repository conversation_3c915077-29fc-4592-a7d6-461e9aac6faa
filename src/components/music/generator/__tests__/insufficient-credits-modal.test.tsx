import { render, screen, fireEvent } from '@testing-library/react';
import { useRouter } from 'next/navigation';
import InsufficientCreditsModal from '../insufficient-credits-modal';

// Mock dependencies
jest.mock('next/navigation');

const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>;
const mockPush = jest.fn();

describe('InsufficientCreditsModal', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    mockUseRouter.mockReturnValue({
      push: mockPush,
      replace: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
      prefetch: jest.fn(),
    } as any);
  });

  const defaultProps = {
    isOpen: true,
    onClose: jest.fn(),
    currentCredits: 1,
    requiredCredits: 3,
    duration: 60,
  };

  test('应该显示模态框标题和描述', () => {
    render(<InsufficientCreditsModal {...defaultProps} />);
    
    expect(screen.getByText('Insufficient Credits')).toBeInTheDocument();
    expect(screen.getByText('You need more credits to generate a 60-second music loop.')).toBeInTheDocument();
  });

  test('应该显示正确的积分信息', () => {
    render(<InsufficientCreditsModal {...defaultProps} />);
    
    expect(screen.getByText('1')).toBeInTheDocument(); // Current credits
    expect(screen.getByText('3')).toBeInTheDocument(); // Required credits
    expect(screen.getByText('2')).toBeInTheDocument(); // Credits needed (3-1)
  });

  test('应该显示时长信息和积分成本', () => {
    render(<InsufficientCreditsModal {...defaultProps} />);
    
    expect(screen.getByText('60-second music loop')).toBeInTheDocument();
    expect(screen.getByText('3 credits')).toBeInTheDocument();
    expect(screen.getByText('Credit costs: 15s = 1 credit, 30s = 2 credits, 60s = 3 credits')).toBeInTheDocument();
  });

  test('应该显示定价计划', () => {
    render(<InsufficientCreditsModal {...defaultProps} />);
    
    expect(screen.getByText('Popular Credit Packs:')).toBeInTheDocument();
    expect(screen.getByText('10 credits')).toBeInTheDocument();
    expect(screen.getByText('50 credits')).toBeInTheDocument();
    expect(screen.getByText('100 credits')).toBeInTheDocument();
    expect(screen.getByText('$9.99')).toBeInTheDocument();
    expect(screen.getByText('$39.99')).toBeInTheDocument();
    expect(screen.getByText('$69.99')).toBeInTheDocument();
  });

  test('应该标记热门计划', () => {
    render(<InsufficientCreditsModal {...defaultProps} />);
    
    expect(screen.getByText('Popular')).toBeInTheDocument();
  });

  test('点击取消按钮应该关闭模态框', () => {
    const onClose = jest.fn();
    render(<InsufficientCreditsModal {...defaultProps} onClose={onClose} />);
    
    const cancelButton = screen.getByRole('button', { name: 'Cancel' });
    fireEvent.click(cancelButton);
    
    expect(onClose).toHaveBeenCalled();
  });

  test('点击获取积分按钮应该跳转到定价页面并关闭模态框', () => {
    const onClose = jest.fn();
    render(<InsufficientCreditsModal {...defaultProps} onClose={onClose} />);
    
    const getCreditsButton = screen.getByRole('button', { name: /Get Credits/i });
    fireEvent.click(getCreditsButton);
    
    expect(onClose).toHaveBeenCalled();
    expect(mockPush).toHaveBeenCalledWith('/pricing');
  });

  test('不同时长应该显示正确的积分成本', () => {
    // Test 15-second duration
    const props15s = { ...defaultProps, duration: 15, requiredCredits: 1 };
    const { rerender } = render(<InsufficientCreditsModal {...props15s} />);
    
    expect(screen.getByText('15-second music loop')).toBeInTheDocument();
    expect(screen.getByText('1 credit')).toBeInTheDocument();

    // Test 30-second duration
    const props30s = { ...defaultProps, duration: 30, requiredCredits: 2 };
    rerender(<InsufficientCreditsModal {...props30s} />);
    
    expect(screen.getByText('30-second music loop')).toBeInTheDocument();
    expect(screen.getByText('2 credits')).toBeInTheDocument();
  });

  test('模态框关闭时不应该显示内容', () => {
    render(<InsufficientCreditsModal {...defaultProps} isOpen={false} />);
    
    expect(screen.queryByText('Insufficient Credits')).not.toBeInTheDocument();
  });

  test('应该正确计算所需积分', () => {
    const props = {
      ...defaultProps,
      currentCredits: 5,
      requiredCredits: 8,
    };
    
    render(<InsufficientCreditsModal {...props} />);
    
    expect(screen.getByText('5')).toBeInTheDocument(); // Current credits
    expect(screen.getByText('8')).toBeInTheDocument(); // Required credits
    expect(screen.getByText('3')).toBeInTheDocument(); // Credits needed (8-5)
  });

  test('积分为复数时应该显示正确的单位', () => {
    const props = {
      ...defaultProps,
      requiredCredits: 1,
    };
    
    render(<InsufficientCreditsModal {...props} />);
    
    expect(screen.getByText('1 credit')).toBeInTheDocument(); // Singular
    
    const propsPlural = {
      ...defaultProps,
      requiredCredits: 3,
    };
    
    const { rerender } = render(<InsufficientCreditsModal {...propsPlural} />);
    rerender(<InsufficientCreditsModal {...propsPlural} />);
    
    expect(screen.getByText('3 credits')).toBeInTheDocument(); // Plural
  });
});
