import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import MusicGeneratorPage from '../music-generator-page';
import { useAppContext } from '@/contexts/app';

// Mock dependencies
jest.mock('next-auth/react');
jest.mock('next/navigation');
jest.mock('sonner');
jest.mock('@/contexts/app');

const mockUseSession = useSession as jest.MockedFunction<typeof useSession>;
const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>;
const mockToast = toast as jest.Mocked<typeof toast>;
const mockUseAppContext = useAppContext as jest.MockedFunction<typeof useAppContext>;

const mockPush = jest.fn();
const mockSetShowSignModal = jest.fn();

// Mock fetch
global.fetch = jest.fn();

describe('MusicGeneratorPage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    mockUseRouter.mockReturnValue({
      push: mockPush,
      replace: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
      prefetch: jest.fn(),
    } as any);

    mockUseAppContext.mockReturnValue({
      setShowSignModal: mockSetShowSignModal,
      user: null,
      showSignModal: false,
    } as any);

    (global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ code: 0, data: { left_credits: 10 } }),
    });
  });

  describe('未登录用户访问控制', () => {
    beforeEach(() => {
      mockUseSession.mockReturnValue({
        data: null,
        status: 'unauthenticated',
        update: jest.fn(),
      });
    });

    test('应该显示登录提示卡片', () => {
      render(<MusicGeneratorPage locale="en" />);
      
      expect(screen.getByText('Sign In Required')).toBeInTheDocument();
      expect(screen.getByText('Please sign in to generate AI music loops and access your generation history.')).toBeInTheDocument();
    });

    test('点击登录按钮应该打开登录模态框', () => {
      render(<MusicGeneratorPage locale="en" />);
      
      const signInButton = screen.getByRole('button', { name: 'Sign In' });
      fireEvent.click(signInButton);
      
      expect(mockSetShowSignModal).toHaveBeenCalledWith(true);
    });

    test('不应该显示音乐生成表单', () => {
      render(<MusicGeneratorPage locale="en" />);
      
      expect(screen.queryByText('Generate Music Loop')).not.toBeInTheDocument();
    });
  });

  describe('已登录用户功能', () => {
    beforeEach(() => {
      mockUseSession.mockReturnValue({
        data: {
          user: {
            uuid: 'test-user-uuid',
            email: '<EMAIL>',
            nickname: 'Test User',
          },
        },
        status: 'authenticated',
        update: jest.fn(),
      });
    });

    test('应该显示音乐生成表单', async () => {
      render(<MusicGeneratorPage locale="en" />);
      
      await waitFor(() => {
        expect(screen.getByText('Generate Music Loop')).toBeInTheDocument();
      });
    });

    test('应该加载用户积分', async () => {
      render(<MusicGeneratorPage locale="en" />);
      
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/user/credits');
      });
    });

    test('应该加载用户生成历史', async () => {
      render(<MusicGeneratorPage locale="en" />);
      
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/music/generations?limit=10');
      });
    });
  });

  describe('积分不足处理', () => {
    beforeEach(() => {
      mockUseSession.mockReturnValue({
        data: {
          user: {
            uuid: 'test-user-uuid',
            email: '<EMAIL>',
            nickname: 'Test User',
          },
        },
        status: 'authenticated',
        update: jest.fn(),
      });

      // Mock insufficient credits response
      (global.fetch as jest.Mock).mockImplementation((url) => {
        if (url === '/api/user/credits') {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({ code: 0, data: { left_credits: 1 } }),
          });
        }
        if (url === '/api/music/generate') {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({
              code: 1,
              message: 'Insufficient credits. Required: 3, Available: 1',
            }),
          });
        }
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ code: 0, data: [] }),
        });
      });
    });

    test('积分不足时应该显示积分不足模态框', async () => {
      render(<MusicGeneratorPage locale="en" />);
      
      // Wait for component to load
      await waitFor(() => {
        expect(screen.getByText('Generate Music Loop')).toBeInTheDocument();
      });

      // Try to generate music with insufficient credits
      const generateButton = screen.getByRole('button', { name: /Generate Music Loop/i });
      fireEvent.click(generateButton);

      await waitFor(() => {
        expect(screen.getByText('Insufficient Credits')).toBeInTheDocument();
      });
    });

    test('积分不足模态框应该显示正确的积分信息', async () => {
      render(<MusicGeneratorPage locale="en" />);
      
      await waitFor(() => {
        expect(screen.getByText('Generate Music Loop')).toBeInTheDocument();
      });

      const generateButton = screen.getByRole('button', { name: /Generate Music Loop/i });
      fireEvent.click(generateButton);

      await waitFor(() => {
        expect(screen.getByText('1')).toBeInTheDocument(); // Current credits
        expect(screen.getByText('2')).toBeInTheDocument(); // Credits needed
      });
    });

    test('点击获取积分按钮应该跳转到定价页面', async () => {
      render(<MusicGeneratorPage locale="en" />);
      
      await waitFor(() => {
        expect(screen.getByText('Generate Music Loop')).toBeInTheDocument();
      });

      const generateButton = screen.getByRole('button', { name: /Generate Music Loop/i });
      fireEvent.click(generateButton);

      await waitFor(() => {
        const getCreditsButton = screen.getByRole('button', { name: /Get Credits/i });
        fireEvent.click(getCreditsButton);
        expect(mockPush).toHaveBeenCalledWith('/pricing');
      });
    });
  });

  describe('音乐生成流程', () => {
    beforeEach(() => {
      mockUseSession.mockReturnValue({
        data: {
          user: {
            uuid: 'test-user-uuid',
            email: '<EMAIL>',
            nickname: 'Test User',
          },
        },
        status: 'authenticated',
        update: jest.fn(),
      });

      // Mock successful generation response
      (global.fetch as jest.Mock).mockImplementation((url) => {
        if (url === '/api/user/credits') {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({ code: 0, data: { left_credits: 10 } }),
          });
        }
        if (url === '/api/music/generate') {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({
              code: 0,
              data: {
                generation_uuid: 'test-generation-uuid',
                status: 'pending',
                estimated_completion_time: 60,
              },
            }),
          });
        }
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ code: 0, data: [] }),
        });
      });
    });

    test('成功生成音乐应该显示成功消息', async () => {
      render(<MusicGeneratorPage locale="en" />);
      
      await waitFor(() => {
        expect(screen.getByText('Generate Music Loop')).toBeInTheDocument();
      });

      const generateButton = screen.getByRole('button', { name: /Generate Music Loop/i });
      fireEvent.click(generateButton);

      await waitFor(() => {
        expect(mockToast.success).toHaveBeenCalledWith('Music generation started!');
      });
    });

    test('生成失败应该显示错误消息', async () => {
      // Mock failed generation response
      (global.fetch as jest.Mock).mockImplementation((url) => {
        if (url === '/api/music/generate') {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({
              code: 1,
              message: 'Generation failed',
            }),
          });
        }
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ code: 0, data: { left_credits: 10 } }),
        });
      });

      render(<MusicGeneratorPage locale="en" />);
      
      await waitFor(() => {
        expect(screen.getByText('Generate Music Loop')).toBeInTheDocument();
      });

      const generateButton = screen.getByRole('button', { name: /Generate Music Loop/i });
      fireEvent.click(generateButton);

      await waitFor(() => {
        expect(mockToast.error).toHaveBeenCalledWith('Failed to start music generation');
      });
    });
  });
});
