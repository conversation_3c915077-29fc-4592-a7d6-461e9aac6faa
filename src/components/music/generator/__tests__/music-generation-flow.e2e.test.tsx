/**
 * 端到端测试：音乐生成页面用户体验流程
 * 测试从未登录用户访问到成功生成音乐的完整流程
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import MusicGeneratorPage from '../music-generator-page';
import { useAppContext } from '@/contexts/app';

// Mock dependencies
jest.mock('next-auth/react');
jest.mock('next/navigation');
jest.mock('sonner');
jest.mock('@/contexts/app');

const mockUseSession = useSession as jest.MockedFunction<typeof useSession>;
const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>;
const mockToast = toast as jest.Mocked<typeof toast>;
const mockUseAppContext = useAppContext as jest.MockedFunction<typeof useAppContext>;

const mockPush = jest.fn();
const mockSetShowSignModal = jest.fn();

// Mock fetch
global.fetch = jest.fn();

describe('音乐生成页面用户体验流程 E2E 测试', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    mockUseRouter.mockReturnValue({
      push: mockPush,
      replace: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
      prefetch: jest.fn(),
    } as any);

    mockUseAppContext.mockReturnValue({
      setShowSignModal: mockSetShowSignModal,
      user: null,
      showSignModal: false,
    } as any);
  });

  describe('完整用户流程：从未登录到成功生成音乐', () => {
    test('流程1：未登录用户访问 -> 登录提示 -> 模态框登录', async () => {
      // 1. 未登录状态
      mockUseSession.mockReturnValue({
        data: null,
        status: 'unauthenticated',
        update: jest.fn(),
      });

      render(<MusicGeneratorPage locale="en" />);

      // 验证显示登录提示
      expect(screen.getByText('Sign In Required')).toBeInTheDocument();
      expect(screen.getByText('Please sign in to generate AI music loops and access your generation history.')).toBeInTheDocument();

      // 点击登录按钮
      const signInButton = screen.getByRole('button', { name: 'Sign In' });
      fireEvent.click(signInButton);

      // 验证打开登录模态框
      expect(mockSetShowSignModal).toHaveBeenCalledWith(true);

      // 2. 模拟用户登录成功
      mockUseSession.mockReturnValue({
        data: {
          user: {
            uuid: 'test-user-uuid',
            email: '<EMAIL>',
            nickname: 'Test User',
          },
        },
        status: 'authenticated',
        update: jest.fn(),
      });

      // Mock API responses for authenticated user
      (global.fetch as jest.Mock).mockImplementation((url) => {
        if (url === '/api/user/credits') {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({ code: 0, data: { left_credits: 10 } }),
          });
        }
        if (url === '/api/music/generations?limit=10') {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({ code: 0, data: [] }),
          });
        }
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ code: 0, data: {} }),
        });
      });

      // 重新渲染以反映登录状态变化
      const { rerender } = render(<MusicGeneratorPage locale="en" />);
      rerender(<MusicGeneratorPage locale="en" />);

      // 验证显示音乐生成表单
      await waitFor(() => {
        expect(screen.getByText('Generate Music Loop')).toBeInTheDocument();
      });

      // 验证加载用户数据
      expect(global.fetch).toHaveBeenCalledWith('/api/user/credits');
      expect(global.fetch).toHaveBeenCalledWith('/api/music/generations?limit=10');
    });

    test('流程2：积分充足用户成功生成音乐', async () => {
      // 已登录用户，积分充足
      mockUseSession.mockReturnValue({
        data: {
          user: {
            uuid: 'test-user-uuid',
            email: '<EMAIL>',
            nickname: 'Test User',
          },
        },
        status: 'authenticated',
        update: jest.fn(),
      });

      // Mock successful API responses
      (global.fetch as jest.Mock).mockImplementation((url) => {
        if (url === '/api/user/credits') {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({ code: 0, data: { left_credits: 10 } }),
          });
        }
        if (url === '/api/music/generate') {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({
              code: 0,
              data: {
                generation_uuid: 'test-generation-uuid',
                status: 'pending',
                estimated_completion_time: 60,
              },
            }),
          });
        }
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ code: 0, data: [] }),
        });
      });

      render(<MusicGeneratorPage locale="en" />);

      // 等待页面加载完成
      await waitFor(() => {
        expect(screen.getByText('Generate Music Loop')).toBeInTheDocument();
      });

      // 填写表单并提交
      const promptTextarea = screen.getByPlaceholderText(/Describe the music you want to generate/i);
      fireEvent.change(promptTextarea, { target: { value: 'Upbeat electronic music' } });

      const generateButton = screen.getByRole('button', { name: /Generate Music Loop/i });
      fireEvent.click(generateButton);

      // 验证成功开始生成
      await waitFor(() => {
        expect(mockToast.success).toHaveBeenCalledWith('Music generation started!');
      });

      // 验证API调用
      expect(global.fetch).toHaveBeenCalledWith('/api/music/generate', expect.objectContaining({
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: expect.stringContaining('Upbeat electronic music'),
      }));
    });

    test('流程3：积分不足用户 -> 积分不足提示 -> 跳转付费页面', async () => {
      // 已登录用户，积分不足
      mockUseSession.mockReturnValue({
        data: {
          user: {
            uuid: 'test-user-uuid',
            email: '<EMAIL>',
            nickname: 'Test User',
          },
        },
        status: 'authenticated',
        update: jest.fn(),
      });

      // Mock insufficient credits responses
      (global.fetch as jest.Mock).mockImplementation((url) => {
        if (url === '/api/user/credits') {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({ code: 0, data: { left_credits: 1 } }),
          });
        }
        if (url === '/api/music/generate') {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({
              code: 1,
              message: 'Insufficient credits. Required: 3, Available: 1',
            }),
          });
        }
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ code: 0, data: [] }),
        });
      });

      render(<MusicGeneratorPage locale="en" />);

      // 等待页面加载完成
      await waitFor(() => {
        expect(screen.getByText('Generate Music Loop')).toBeInTheDocument();
      });

      // 选择60秒时长（需要3积分）
      const durationSelect = screen.getByDisplayValue('30');
      fireEvent.change(durationSelect, { target: { value: '60' } });

      // 填写表单并提交
      const promptTextarea = screen.getByPlaceholderText(/Describe the music you want to generate/i);
      fireEvent.change(promptTextarea, { target: { value: 'Upbeat electronic music' } });

      const generateButton = screen.getByRole('button', { name: /Generate Music Loop/i });
      fireEvent.click(generateButton);

      // 验证显示积分不足模态框
      await waitFor(() => {
        expect(screen.getByText('Insufficient Credits')).toBeInTheDocument();
      });

      // 验证积分信息显示正确
      expect(screen.getByText('1')).toBeInTheDocument(); // Current credits
      expect(screen.getByText('3')).toBeInTheDocument(); // Required credits
      expect(screen.getByText('2')).toBeInTheDocument(); // Credits needed

      // 点击获取积分按钮
      const getCreditsButton = screen.getByRole('button', { name: /Get Credits/i });
      fireEvent.click(getCreditsButton);

      // 验证跳转到定价页面
      expect(mockPush).toHaveBeenCalledWith('/pricing');
    });

    test('流程4：表单预检查 - 积分不足时禁用生成按钮', async () => {
      // 已登录用户，积分不足
      mockUseSession.mockReturnValue({
        data: {
          user: {
            uuid: 'test-user-uuid',
            email: '<EMAIL>',
            nickname: 'Test User',
          },
        },
        status: 'authenticated',
        update: jest.fn(),
      });

      // Mock insufficient credits
      (global.fetch as jest.Mock).mockImplementation((url) => {
        if (url === '/api/user/credits') {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({ code: 0, data: { left_credits: 1 } }),
          });
        }
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ code: 0, data: [] }),
        });
      });

      render(<MusicGeneratorPage locale="en" />);

      // 等待页面加载完成
      await waitFor(() => {
        expect(screen.getByText('Generate Music Loop')).toBeInTheDocument();
      });

      // 选择60秒时长（需要3积分，但用户只有1积分）
      const durationSelect = screen.getByDisplayValue('30');
      fireEvent.change(durationSelect, { target: { value: '60' } });

      // 验证生成按钮被禁用
      const generateButton = screen.getByRole('button', { name: /Generate Music Loop/i });
      expect(generateButton).toBeDisabled();

      // 验证显示积分不足提示
      expect(screen.getByText(/Insufficient credits. You need 2 more credits./)).toBeInTheDocument();

      // 验证显示获取积分链接
      const getMoreCreditsLink = screen.getByRole('link', { name: 'Get More Credits' });
      expect(getMoreCreditsLink).toHaveAttribute('href', '/pricing');
    });
  });
});
