"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  Download, 
  Share2, 
  Heart, 
  Clock, 
  Music, 
  Verified,
  TrendingUp,
  User,
  Calendar,
  Headphones,
  Star
} from "lucide-react";
import { cn } from "@/lib/utils";
import LoopTestPlayer from "../player/loop-test-player";
import AudioPlayer from "../player/audio-player";
import { SimpleWaveform } from "../waveform/waveform-display";
import StemManager from "../stems/stem-manager";
import VariationManager from "../variations/variation-manager";
import LicenseManager from "../license/license-manager";
import WatermarkInfo from "../watermark/watermark-info";
import { toast } from "sonner";
import { Track } from "@/types/music";

interface TrackDetailPageProps {
  track: Track;
  locale: string;
}

export default function TrackDetailPage({ track, locale }: TrackDetailPageProps) {
  const [isLiked, setIsLiked] = useState(false);
  const [downloadCount, setDownloadCount] = useState(track.download_count || 0);
  const [showLoopTest, setShowLoopTest] = useState(true);

  const formatDate = (dateString?: string) => {
    if (!dateString) return "Unknown";
    return new Date(dateString).toLocaleDateString(locale === "en" ? "en-US" : locale, {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return "Unknown";
    const mb = bytes / (1024 * 1024);
    return `${mb.toFixed(1)} MB`;
  };

  const handleDownload = async () => {
    try {
      // Increment download count
      setDownloadCount(prev => prev + 1);
      
      // Trigger download
      const link = document.createElement("a");
      link.href = track.file_url;
      link.download = `${track.title || "loop"}.${track.file_format || "mp3"}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      toast.success("Download started!");
    } catch (error) {
      toast.error("Download failed. Please try again.");
      console.error("Download error:", error);
    }
  };

  const handleShare = async () => {
    try {
      if (navigator.share) {
        await navigator.share({
          title: track.title || "AI Music Loop",
          text: track.prompt || "Check out this AI-generated music loop!",
          url: window.location.href,
        });
      } else {
        // Fallback: copy to clipboard
        await navigator.clipboard.writeText(window.location.href);
        toast.success("Link copied to clipboard!");
      }
    } catch (error) {
      toast.error("Sharing failed. Please try again.");
      console.error("Share error:", error);
    }
  };

  const handleLike = () => {
    setIsLiked(!isLiked);
    toast.success(isLiked ? "Removed from favorites" : "Added to favorites");
  };

  const handleLoopTestComplete = (loopCount: number) => {
    toast.success(`Loop test completed! Played ${loopCount} seamless loops.`);
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="space-y-4">
          <div className="flex items-start justify-between">
            <div className="space-y-2">
              <h1 className="text-3xl font-bold tracking-tight">
                {track.title || "Untitled Loop"}
              </h1>
              <p className="text-lg text-muted-foreground">
                {track.prompt || "AI-generated seamless music loop"}
              </p>
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleLike}
                className={cn(isLiked && "text-red-500")}
              >
                <Heart className={cn("h-4 w-4", isLiked && "fill-current")} />
              </Button>
              <Button variant="outline" size="sm" onClick={handleShare}>
                <Share2 className="h-4 w-4" />
              </Button>
              <Button onClick={handleDownload}>
                <Download className="h-4 w-4 mr-2" />
                Download
              </Button>
            </div>
          </div>

          {/* Track Metadata */}
          <div className="flex flex-wrap gap-2">
            {track.style && (
              <Badge variant="secondary">
                <Music className="h-3 w-3 mr-1" />
                {track.style}
              </Badge>
            )}
            {track.mood && (
              <Badge variant="outline">
                {track.mood}
              </Badge>
            )}
            {track.bpm && (
              <Badge variant="outline">
                {track.bpm} BPM
              </Badge>
            )}
            <Badge variant="outline">
              <Clock className="h-3 w-3 mr-1" />
              {track.duration}s
            </Badge>
            {track.loop_verification?.is_seamless && (
              <Badge variant="default" className="bg-green-500">
                <Verified className="h-3 w-3 mr-1" />
                Seamless Loop
              </Badge>
            )}
          </div>
        </div>

        {/* Loop Test Player */}
        {showLoopTest && (
          <LoopTestPlayer
            track={track}
            autoStart={false}
            maxLoops={3}
            onTestComplete={handleLoopTestComplete}
            onLoopComplete={(loop) => console.log(`Loop ${loop} completed`)}
          />
        )}

        {/* Regular Audio Player */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Headphones className="h-5 w-5" />
              Audio Player
            </CardTitle>
          </CardHeader>
          <CardContent>
            <AudioPlayer
              track={track}
              showWaveform={true}
              showDownload={false}
            />
          </CardContent>
        </Card>

        {/* Track Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Loop Verification Results */}
          {track.loop_verification && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Verified className="h-5 w-5" />
                  Loop Verification
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Seamless Quality</span>
                  <Badge 
                    variant={track.loop_verification.is_seamless ? "default" : "destructive"}
                    className={track.loop_verification.is_seamless ? "bg-green-500" : ""}
                  >
                    {track.loop_verification.is_seamless ? "Seamless" : "Not Seamless"}
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Verification Score</span>
                  <div className="flex items-center gap-2">
                    <Star className="h-4 w-4 text-yellow-500" />
                    <span className="text-sm">
                      {(parseFloat(track.loop_verification.verification_score) * 100).toFixed(1)}%
                    </span>
                  </div>
                </div>

                {track.loop_verification.verification_method && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Method</span>
                    <span className="text-sm text-muted-foreground">
                      {track.loop_verification.verification_method}
                    </span>
                  </div>
                )}

                {track.loop_verification.verified_at && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Verified</span>
                    <span className="text-sm text-muted-foreground">
                      {formatDate(track.loop_verification.verified_at)}
                    </span>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Track Statistics */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Track Statistics
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Downloads</span>
                <span className="text-sm">{downloadCount.toLocaleString()}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">File Size</span>
                <span className="text-sm">{formatFileSize(track.file_size)}</span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Format</span>
                <span className="text-sm uppercase">{track.file_format || "MP3"}</span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Created</span>
                <span className="text-sm">{formatDate(typeof track.created_at === 'string' ? track.created_at : track.created_at?.toISOString())}</span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Visibility</span>
                <Badge variant={track.is_public ? "default" : "secondary"}>
                  {track.is_public ? "Public" : "Private"}
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Stem Manager */}
        <StemManager track={track} />

        {/* Variation Manager */}
        <VariationManager track={track} />

        {/* License Manager */}
        <LicenseManager track={track} />

        {/* Watermark Information */}
        <WatermarkInfo track={track} userPlan="free" />

        {/* Waveform Visualization */}
        {track.waveform_data && (
          <Card>
            <CardHeader>
              <CardTitle>Waveform</CardTitle>
            </CardHeader>
            <CardContent>
              <SimpleWaveform
                peaks={track.waveform_data.peaks}
                duration={track.duration}
                height={120}
                interactive={false}
              />
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
